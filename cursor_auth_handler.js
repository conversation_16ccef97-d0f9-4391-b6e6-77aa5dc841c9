/**
 * Cursor 认证处理器
 * 基于对 workbench.desktop.main.js 的分析，展示如何处理登录API返回的认证信息
 */

class CursorAuthHandler {
    constructor() {
        // 模拟存储服务（在实际Cursor代码中是 this.a）
        this.storage = new Map();
        this.isAuthenticated = false;
        this.authListeners = [];
    }

    /**
     * 处理从 /auth/poll API 返回的认证数据
     * @param {Object} authResponse - API 返回的认证响应
     */
    async handleAuthResponse(authResponse) {
        const {
            accessToken,
            refreshToken,
            authId,
            challenge,
            uuid
        } = authResponse;

        console.log('收到认证响应:', {
            uuid,
            authId,
            hasAccessToken: !!accessToken,
            hasRefreshToken: !!refreshToken
        });

        try {
            // 1. 验证必要字段
            if (!accessToken || !refreshToken) {
                throw new Error('缺少必要的 token 信息');
            }

            // 2. 设置 authId（相当于 this.Hb(authId)）
            if (authId) {
                this.setAuthId(authId);
            }

            // 3. 存储 tokens（相当于 this.y(accessToken, refreshToken)）
            this.storeTokens(accessToken, refreshToken);

            // 4. 刷新会员类型和其他认证状态
            await this.refreshMembershipType();

            // 5. 触发认证状态变更通知
            this.notifyAuthStateChanged();

            console.log('✅ 登录认证成功');
            return true;

        } catch (error) {
            console.error('❌ 认证处理失败:', error);
            return false;
        }
    }

    /**
     * 存储访问令牌和刷新令牌
     * 对应代码: this.y = (n, r) => { this.a.store("cursorAuth/refreshToken", r, -1, 1), this.a.store("cursorAuth/accessToken", n, -1, 1); }
     */
    storeTokens(accessToken, refreshToken) {
        // 存储刷新令牌
        this.storage.set("cursorAuth/refreshToken", refreshToken);
        
        // 存储访问令牌
        this.storage.set("cursorAuth/accessToken", accessToken);
        
        this.isAuthenticated = true;
        
        console.log('🔐 Tokens 已存储');
    }

    /**
     * 设置认证ID
     * 对应代码: this.Hb(authId)
     */
    setAuthId(authId) {
        this.storage.set("cursorAuth/authId", authId);
        console.log('👤 AuthId 已设置:', authId);
    }

    /**
     * 获取访问令牌
     * 对应代码: this.u = () => this.s ? this.s : this.c.overrideCursorAuthToken ? this.c.overrideCursorAuthToken : this.a.get("cursorAuth/accessToken", -1)
     */
    getAccessToken() {
        return this.storage.get("cursorAuth/accessToken");
    }

    /**
     * 获取刷新令牌
     * 对应代码: this.t = () => this.a.get("cursorAuth/refreshToken", -1)
     */
    getRefreshToken() {
        return this.storage.get("cursorAuth/refreshToken");
    }

    /**
     * 刷新访问令牌
     * 对应代码: this.refreshAccessToken
     */
    async refreshAccessToken() {
        const refreshToken = this.getRefreshToken();
        if (!refreshToken) {
            console.error('❌ 没有刷新令牌，无法刷新');
            return false;
        }

        try {
            // 调用刷新 API
            const response = await fetch('https://api2.cursor.sh/auth/refresh', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    refreshToken: refreshToken
                })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.accessToken) {
                    this.storage.set("cursorAuth/accessToken", data.accessToken);
                    console.log('🔄 访问令牌已刷新');
                    return true;
                }
            }
            
            console.error('❌ 刷新令牌失败');
            return false;
        } catch (error) {
            console.error('❌ 刷新令牌时出错:', error);
            return false;
        }
    }

    /**
     * 刷新会员类型
     * 对应代码: await this.refreshMembershipType()
     */
    async refreshMembershipType() {
        const accessToken = this.getAccessToken();
        if (!accessToken) return;

        try {
            // 获取用户信息和会员类型
            const response = await fetch('https://api2.cursor.sh/user/info', {
                headers: {
                    'Authorization': `Bearer ${accessToken}`
                }
            });

            if (response.ok) {
                const userInfo = await response.json();
                this.storage.set("cursorAuth/membershipType", userInfo.membershipType || "FREE");
                this.storage.set("cursorAuth/cachedEmail", userInfo.email);
                console.log('👑 会员类型已更新:', userInfo.membershipType);
            }
        } catch (error) {
            console.error('❌ 获取会员信息失败:', error);
        }
    }

    /**
     * 注销登录
     * 对应代码: this.a.store("cursorAuth/refreshToken", null, -1, 1), this.a.store("cursorAuth/accessToken", null, -1, 1)
     */
    logout() {
        // 清除所有认证相关的存储
        this.storage.delete("cursorAuth/refreshToken");
        this.storage.delete("cursorAuth/accessToken");
        this.storage.delete("cursorAuth/authId");
        this.storage.delete("cursorAuth/membershipType");
        this.storage.delete("cursorAuth/cachedEmail");
        
        this.isAuthenticated = false;
        
        console.log('🚪 已登出');
        this.notifyAuthStateChanged();
    }

    /**
     * 添加认证状态变更监听器
     */
    addAuthStateListener(callback) {
        this.authListeners.push(callback);
    }

    /**
     * 通知认证状态变更
     */
    notifyAuthStateChanged() {
        this.authListeners.forEach(callback => {
            try {
                callback(this.isAuthenticated);
            } catch (error) {
                console.error('认证状态监听器出错:', error);
            }
        });
    }

    /**
     * 获取用于API请求的Bearer token
     */
    getBearerToken() {
        const token = this.getAccessToken();
        return token ? `Bearer ${token}` : null;
    }

    /**
     * 检查token是否有效
     */
    async validateToken() {
        const accessToken = this.getAccessToken();
        if (!accessToken) return false;

        try {
            const response = await fetch('https://api2.cursor.sh/auth/validate', {
                headers: {
                    'Authorization': `Bearer ${accessToken}`
                }
            });
            return response.ok;
        } catch (error) {
            console.error('Token 验证失败:', error);
            return false;
        }
    }
}

/**
 * 使用示例：处理你提供的认证数据
 */
async function handleLogin() {
    const authHandler = new CursorAuthHandler();
    
    // 你从 API 获取到的认证数据
    const authResponse = {
        accessToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJnb29nbGUtb2F1dGgyfHVzZXJfMDFKRVdWQjczTTNGOVZLRjRCN1RKNk1QWlEiLCJ0aW1lIjoiMTc0ODQ5ODM1NSIsInJhbmRvbW5lc3MiOiJmNzgzY2FiMC1lYmVhLTRkOGMiLCJleHAiOjE3NTM2ODIzNTUsImlzcyI6Imh0dHBzOi8vYXV0aGVudGljYXRpb24uY3Vyc29yLnNoIiwic2NvcGUiOiJvcGVuaWQgcHJvZmlsZSBlbWFpbCBvZmZsaW5lX2FjY2VzcyIsImF1ZCI6Imh0dHBzOi8vY3Vyc29yLmNvbSIsInR5cGUiOiJzZXNzaW9uIn0.Ixb2D_Y-oJ-xz3P77MO5oL6Et_28G8ByQjtDRpOaLnM",
        authId: "google-oauth2|user_01JEWVB73M3F9VKF4B7TJ6MPZQ",
        challenge: "ywgeyJBj6uiliImFv-wLV56QLIczr7yA4MNQ46G7gQc",
        refreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJnb29nbGUtb2F1dGgyfHVzZXJfMDFKRVdWQjczTTNGOVZLRjRCN1RKNk1QWlEiLCJ0aW1lIjoiMTc0ODQ5ODM1NSIsInJhbmRvbW5lc3MiOiJmNzgzY2FiMC1lYmVhLTRkOGMiLCJleHAiOjE3NTM2ODIzNTUsImlzcyI6Imh0dHBzOi8vYXV0aGVudGljYXRpb24uY3Vyc29yLnNoIiwic2NvcGUiOiJvcGVuaWQgcHJvZmlsZSBlbWFpbCBvZmZsaW5lX2FjY2VzcyIsImF1ZCI6Imh0dHBzOi8vY3Vyc29yLmNvbSIsInR5cGUiOiJzZXNzaW9uIn0.Ixb2D_Y-oJ-xz3P77MO5oL6Et_28G8ByQjtDRpOaLnM",
        uuid: "6811c665-8cd5-425f-aa12-6ed5b4dd70cd"
    };

    // 处理认证响应
    const success = await authHandler.handleAuthResponse(authResponse);
    
    if (success) {
        console.log('✅ 登录流程完成！');
        
        // 现在可以使用认证信息进行API调用
        const bearerToken = authHandler.getBearerToken();
        console.log('🔑 Bearer Token:', bearerToken);
        
        // 验证token是否有效
        const isValid = await authHandler.validateToken();
        console.log('✓ Token 有效性:', isValid);
    }
}

// 导出类和示例函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CursorAuthHandler, handleLogin };
} else {
    window.CursorAuthHandler = CursorAuthHandler;
    window.handleLogin = handleLogin;
} 